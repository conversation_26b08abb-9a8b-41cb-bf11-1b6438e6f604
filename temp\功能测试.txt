CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_gytj`(IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_yp_tj VARCHAR(20))
    READS SQL DATA
    DETERMINISTIC
    COMMENT '给药途径分析存储过程'
main_block: BEGIN
		DECLARE v_sda_id VARCHAR(10);
		DECLARE v_by_code VARCHAR(10);
		DECLARE v_n_count INT;
		DECLARE v_n_count1 INT;
		DECLARE v_n_count2 INT;
		DECLARE v_n_count3 INT;
		DECLARE v_n_count4 INT;
		DECLARE v_n_count5 INT;
		DECLARE v_n_count6 INT;
		DECLARE v_n_count7 INT;
		DECLARE v_n_count8 INT;
		DECLARE v_n_count9 INT;
		DECLARE v_n_count10 INT;
		DECLARE v_n_count11 INT;
		DECLARE v_n_count12 INT;
		DECLARE v_adm_name VARCHAR(50);
		DECLARE v_count7 INT;
		DECLARE v_by_code_2 VARCHAR(10);
		DECLARE v_zx_flag VARCHAR(10);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_gytj_bs VARCHAR(10);
		
		-- 异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				-- 如果出现异常，继续执行
				BEGIN END;
		END;
		
		-- 获取药品基本信息
		SELECT DRUG_NAME, zx_flag INTO v_ywa_name, v_zx_flag 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code
		LIMIT 1;
		
		-- 获取标准数据信息
		SELECT b.sda_id, a.gytj_bs INTO v_sda_id, v_gytj_bs 
		FROM rms_t_sda a, rms_t_byyydzb b 
		WHERE a.ID = b.sda_id AND yp_code = p_yp_code
		LIMIT 1;
		
		-- 获取给药途径编码
		SELECT by_code INTO v_by_code 
		FROM rms_t_tjdzb 
		WHERE h_tj = p_yp_tj
		LIMIT 1;
		
		-- 如果没有标准数据ID则返回
		IF v_sda_id = '' OR v_sda_id IS NULL THEN
				LEAVE main_block;
		END IF;
		
		-- 中药且给药途径标识为0则返回
		IF v_zx_flag = '3' AND v_gytj_bs = '0' THEN
				LEAVE main_block;
		END IF;
		
		-- 检查中药颗粒/免煎/粉/M/Z的特殊情况
		SELECT COUNT(1) INTO v_n_count4 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND zx_flag = '3' 
		AND (drug_name LIKE '%颗粒%' OR drug_name LIKE '%免煎%' 
				OR drug_name LIKE '%粉%' OR drug_name LIKE '%M%' 
				OR drug_name LIKE '%Z%');
		
		IF v_n_count4 > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 检查中药足浴/外用/打粉的特殊情况
		SELECT COUNT(1) INTO v_count7 
		FROM rms_t_pres_med 
		WHERE Code = p_Code AND his_code = p_yp_code 
		AND (yysm IN ('足浴','外用','打粉')
			OR yysm LIKE '%外%'
			OR yysm LIKE '%打粉%');
		
		IF v_count7 > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否为溶媒
		SELECT COUNT(1) INTO v_n_count6 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND is_rm = '1';
		
		IF v_n_count6 > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否是中药
		SELECT COUNT(1) INTO v_n_count3 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND ZX_FLAG = '3';
		
		-- 检查是否有给药途径设置
		SELECT COUNT(1) INTO v_n_count11 
		FROM rms_t_sda_gytj a
		WHERE a.sda_id = v_sda_id;
		
		-- 如果没有给药途径设置且是中药且给药途径不为空，提示内服不能无煎药方式
		IF v_n_count11 = 0 AND v_n_count3 = 1 AND p_yp_tj != '' THEN
				INSERT INTO rms_t_pres_fx (
						Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				VALUES (
						p_Code, v_ywa_name, '', '1', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
						'草药煎药方法错误', 
						CONCAT('【', v_ywa_name, '】无需特殊煎药方式'), '0', '给药途径错误'
				);
				LEAVE main_block;
		END IF;
		
		-- 如果没有给药途径设置则返回
		IF v_n_count11 = 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 如果给药途径为空且不是中药则返回
		IF (v_by_code = '' OR v_by_code IS NULL) AND v_n_count3 = 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否有自定义给药途径
		IF EXISTS(SELECT 1 FROM rms_t_med_zdy_gytj WHERE yp_code = p_yp_code AND gytj_code = p_yp_tj) THEN
				LEAVE main_block;
		END IF;
		
		-- 中药给药途径检查
		IF v_n_count3 = 1 THEN
				SELECT COUNT(1) INTO v_n_count
				FROM rms_t_sda b, rms_t_sda_gytj a 
				WHERE a.sda_id = b.ID 
				AND b.id = v_sda_id
				AND a.gytj_code = p_yp_tj
				AND a.bs = '0';
				
				IF v_n_count > 0 THEN
						LEAVE main_block;
				END IF;
		END IF;
		
		-- 西药给药途径检查
		IF v_n_count3 = 0 THEN
				SELECT COUNT(1) INTO v_n_count
				FROM rms_t_sda b, rms_t_sda_gytj a 
				WHERE a.sda_id = b.ID 
				AND b.id = v_sda_id
				AND a.gytj_code IN (
						SELECT by_code FROM rms_t_tjdzb WHERE h_tj = p_yp_tj
				)
				AND a.bs = '0';
				
				IF v_n_count > 0 THEN
						LEAVE main_block;
				END IF;
		END IF;
		
		-- 中药煎药方式为空的处理
		IF v_zx_flag = '3' AND v_gytj_bs = '1' AND p_yp_tj = '' THEN
				INSERT INTO rms_t_pres_fx (
						Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				VALUES (
						p_Code, v_ywa_name, '', '1', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
						'草药煎药方法错误',
						CONCAT('【', v_ywa_name, '】的煎药方式不能为空，建议使用【', rms_get_gytj(v_sda_id), '】煎药方法'), 
						'0', '给药途径错误'
				);
				LEAVE main_block;
		END IF;
		
		-- 中药给药途径不匹配的处理
		IF v_n_count3 > 0 AND v_n_count = 0 AND v_gytj_bs = '1' THEN
				INSERT INTO rms_t_pres_fx (
						Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				VALUES (
						p_Code, v_ywa_name, '', '1', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
						'草药煎药方法错误',
						CONCAT('【', v_ywa_name, '】【中国药典2020版】未提及该煎药方法！，建议使用【', rms_get_gytj(v_sda_id), '】煎药方法'), 
						'0', '给药途径错误'
				);
				LEAVE main_block;
		END IF;
		
		-- 获取其他检查参数
		SELECT COUNT(1) INTO v_n_count10 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code 
		AND DRUG_NAME LIKE '%醋酸戈舍%';
		
		SELECT COUNT(1) INTO v_n_count7 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code 
		AND DRUG_FOR_NAME NOT LIKE '%注射%' 
		AND DRUG_FOR_NAME NOT LIKE '%针%';
		
		SELECT SUBSTRING(by_code, 1, 2) INTO v_by_code_2 
		FROM rms_t_tjdzb 
		WHERE akb020 = p_akb020 AND h_tj = p_yp_tj
		LIMIT 1;
		
		SELECT COUNT(1) INTO v_n_count8 
		FROM rms_t_sda a, rms_t_byyydzb b 
		WHERE b.yp_code = p_yp_code AND a.ID = b.sda_id 
		AND (a.ym LIKE '%胰岛%' OR a.ym LIKE '%膏%');
		
		-- 西药特殊给药途径检查
		IF v_n_count3 = 0 THEN
				IF (v_n_count10 = 0 AND v_n_count7 > 0 AND v_by_code_2 = '02') 
				OR (v_n_count8 > 0 AND v_by_code_2 = '01') THEN
						INSERT INTO rms_t_pres_fx (
								Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
						)
						VALUES (
								p_Code, v_ywa_name, '', '0', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
								CONCAT('【', v_ywa_name, '】给药途径错误'),
								CONCAT('【', v_ywa_name, '】说明书未提及该给药途径'), '0', '给药途径错误'
						);
						LEAVE main_block;
				END IF;
		END IF;
		
		-- 默认给药途径错误处理
		INSERT INTO rms_t_pres_fx (
				Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
		)
		VALUES (
				p_Code, v_ywa_name, '', '1', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
				CONCAT('【', v_ywa_name, '】给药途径错误'),
				CONCAT('【', v_ywa_name, '】说明书未提及该给药途径'), '0', '给药途径错误'
		);
END